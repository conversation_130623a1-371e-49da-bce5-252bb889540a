'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useShadowDOM } from '@/hooks/useShadowDOM';

interface ZoomableResultProps {
  content: string;
  fontFamily?: string;
}

const ZoomableResult: React.FC<ZoomableResultProps> = ({ content, fontFamily }) => {
  const [zoom, setZoom] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Inject additional styles into the content for Shadow DOM
  const additionalStyles = `
    .a4-page {
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
  `;
  const contentWithStyles = `<style>${additionalStyles}</style>${content}`;
  
  // Use Shadow DOM for style isolation
  const shadowContainerRef = useShadowDOM({ html: contentWithStyles });

  const minZoom = 0.1;
  const maxZoom = 3;
  const zoomStep = 0.1;

  // Handle pinch to zoom
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    let initialDistance = 0;
    let initialZoom = 1;

    const getDistance = (touch1: Touch, touch2: Touch) => {
      const dx = touch1.clientX - touch2.clientX;
      const dy = touch1.clientY - touch2.clientY;
      return Math.sqrt(dx * dx + dy * dy);
    };

    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 2) {
        e.preventDefault();
        initialDistance = getDistance(e.touches[0], e.touches[1]);
        initialZoom = zoom;
      }
    };

    const handleTouchMove = (e: TouchEvent) => {
      if (e.touches.length === 2) {
        e.preventDefault();
        const currentDistance = getDistance(e.touches[0], e.touches[1]);
        const scale = (currentDistance / initialDistance) * initialZoom;
        setZoom(Math.min(Math.max(scale, minZoom), maxZoom));
      }
    };

    // Mouse wheel zoom with Ctrl key
    const handleWheel = (e: WheelEvent) => {
      if (e.ctrlKey || e.metaKey) {
        e.preventDefault();
        const delta = e.deltaY > 0 ? -zoomStep : zoomStep;
        setZoom(Math.min(Math.max(zoom + delta, minZoom), maxZoom));
      }
    };

    if (container) {
      container.addEventListener('touchstart', handleTouchStart, { passive: false });
      container.addEventListener('touchmove', handleTouchMove, { passive: false });
      container.addEventListener('wheel', handleWheel, { passive: false });

      return () => {
        container.removeEventListener('touchstart', handleTouchStart);
        container.removeEventListener('touchmove', handleTouchMove);
        container.removeEventListener('wheel', handleWheel);
      };
    }
    return undefined;
  }, [zoom]);

  // Handle mouse drag for panning when zoomed in
  const handleMouseDown = (e: React.MouseEvent) => {
    if (zoom > 1) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoom > 1) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const zoomIn = () => setZoom(Math.min(zoom + zoomStep, maxZoom));
  const zoomOut = () => setZoom(Math.max(zoom - zoomStep, minZoom));
  const resetZoom = () => {
    setZoom(1);
    setPosition({ x: 0, y: 0 });
  };

  return (
    <div className="w-full h-full flex flex-col">
      <div className="flex flex-wrap items-center justify-center gap-1 sm:gap-2 py-2 px-2 sm:px-3 bg-gray-50">
        <button 
          onClick={zoomOut}
          disabled={zoom <= minZoom}
          className="p-1 sm:p-1.5 bg-gray-200 hover:bg-gray-300 rounded text-xs sm:text-sm flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Perkecil"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
          <span className="hidden xs:inline">Perkecil</span>
        </button>
        <span className="px-1.5 sm:px-2 py-1 bg-white rounded text-xs sm:text-sm font-medium min-w-[48px] text-center">
          {Math.round(zoom * 100)}%
        </span>
        <button 
          onClick={zoomIn}
          disabled={zoom >= maxZoom}
          className="p-1 sm:p-1.5 bg-gray-200 hover:bg-gray-300 rounded text-xs sm:text-sm flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Perbesar"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          <span className="hidden xs:inline">Perbesar</span>
        </button>
        <button 
          onClick={resetZoom}
          className="p-1 bg-gray-200 hover:bg-gray-300 rounded text-xs sm:text-sm flex items-center gap-1"
          aria-label="Reset"
        >
          Reset
        </button>
      </div>
      <div 
        ref={containerRef}
        className="relative flex-1 overflow-auto touch-manipulation"
        style={{ cursor: zoom > 1 ? (isDragging ? 'grabbing' : 'grab') : 'default' }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div 
          className="design-preview transition-transform duration-75"
          style={{
            transform: `scale(${zoom})`,
            transformOrigin: 'top left',
            width: zoom <= 1 ? '100%' : `${Math.max(100, 100 * zoom)}%`,
            height: zoom <= 1 ? '100%' : `${Math.max(100, 100 * zoom)}%`,
            position: 'relative',
            left: position.x,
            top: position.y,
          }}
        >
          <div 
            style={{ 
              padding: '16px',
              boxSizing: 'border-box',
              width: '100%',
            }}
          >
            <div
              ref={shadowContainerRef}
              style={{
                // Force the content to be at least the full width to make padding effective
                minWidth: '100%',
                // Add extra margin to ensure content always has space on right side
                marginRight: '16px',
                display: 'inline-block',
                fontFamily
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZoomableResult;
