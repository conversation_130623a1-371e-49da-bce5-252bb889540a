import puppeteer from "puppeteer";

/**
 * <PERSON><PERSON> provided HTML string to a PDF buffer using Puppeteer headless Chromium.
 * This launches a browser, opens a new page with HTML content via data URL, and prints to PDF.
 * Callers are responsible for catching any errors.
 */
export async function renderHtmlToPdf(html: string): Promise<Uint8Array> {
  const browser = await puppeteer.launch({
    args: [
      "--no-sandbox",
      "--disable-setuid-sandbox",
      "--disable-dev-shm-usage",
      "--disable-gpu",
    ],
  });
  try {
    const page = await browser.newPage();

    // Use a data URL to avoid writing temp files
    const dataUri = `data:text/html;charset=utf-8,${encodeURIComponent(html)}`;
    await page.goto(dataUri, { waitUntil: "networkidle0" });

    const pdfBytes = await page.pdf({ format: "A4", printBackground: true });
    return pdfBytes;
  } finally {
    await browser.close();
  }
}
