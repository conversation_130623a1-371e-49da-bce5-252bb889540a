import { NextRequest, NextResponse } from 'next/server';
import chromium from '@sparticuz/chromium-min';

// We will import puppeteer dynamically based on environment so that:
// - In serverless/Edge environments we keep using `puppeteer-core` with the lightweight @sparticuz/chromium-min binary
// - In local development (e.g., Windows) we fall back to full `puppeteer`, which bundles its own Chromium
// This prevents ENOENT errors when the @sparticuz binary is not available for the host OS.

export async function POST(request: NextRequest) {
  try {
    // Get HTML content from request body
    const { html, fileName = 'document' } = await request.json();
    
    if (!html) {
      return NextResponse.json(
        { error: 'HTML content is required' },
        { status: 400 }
      );
    }

    // ---------- Launch browser depending on environment ----------
    // In most serverless/edge runtimes (Netlify, Vercel, AWS Lambda) we rely on
    // @sparticuz/chromium + puppeteer-core (small binary, faster cold starts).
    // On local development (especially Windows) that binary is not available,
    // so we gracefully fall back to the full `puppeteer` package which ships
    // its own Chromium. This avoids the ENOENT error you observed.

    let browser;
    let page;

    const isServerless = !!process.env.AWS_REGION || !!process.env.NETLIFY || !!process.env.VERCEL;
    const isWindowsLocal = process.platform === 'win32' && !isServerless;

    if (isWindowsLocal) {
      // Dynamically import full puppeteer to avoid increasing bundle size for prod.
      const puppeteer = (await import('puppeteer')).default;
      browser = await puppeteer.launch({
        args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage'],
        headless: true,
      });

      // Create a new page
      page = await browser.newPage();

      // Wait for all fonts to be loaded
      await page.evaluateHandle('document.fonts.ready');
    } else {
      // Serverless / Linux builds – keep lightweight setup.
      const puppeteerCore = (await import('puppeteer-core')).default;
      browser = await puppeteerCore.launch({
        args: chromium.args,
        headless: 'shell',
        executablePath:  await chromium.executablePath("https://github.com/Sparticuz/chromium/releases/download/v137.0.1/chromium-v137.0.1-pack.x64.tar"),
      });

      // Create a new page
      page = await browser.newPage();

      // Wait for all fonts to be loaded
      await page.evaluateHandle('document.fonts.ready');
    }
    
    // Set content to the page
    await page.setContent(html, {
      waitUntil: 'networkidle0', // Wait until network is idle
    });

    // Set page size to A4
    await page.setViewport({
      width: 794, // A4 width in pixels (72 dpi)
      height: 1123, // A4 height in pixels (72 dpi)
      deviceScaleFactor: 2, // Higher scale for better quality
    });

    // Generate PDF
    const pdf = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '0',
        right: '0',
        bottom: '0',
        left: '0',
      },
    });

    // Close browser
    await browser.close();

    // Return PDF as response
    return new NextResponse(pdf, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${fileName}.pdf"`,
      },
    });
  } catch (error) {
    console.error('PDF generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' },
      { status: 500 }
    );
  }
}